<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>
<html>
<head>
<title>棉花棠</title>
<meta name="keywords" content="棉花棠" />
<meta name="description" content="棉花棠" />
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

<script type="text/javascript" src="/Public/js/jquery-1.11.1.min.js"></script>

<script type="text/javascript" src="/Public/js/swiper.min.js"></script>

<link rel="stylesheet" type="text/css" href="/Public/css/css.css" />

<link rel="stylesheet" type="text/css" href="/Public/css/swiper.min.css">
<link rel="icon" type="image/x-icon" href="/Public/images/favicon.ico">
<script type="text/javascript" src="/Public/js/ion.rangeSlider.min.js"></script>
<link rel="stylesheet" type="text/css" href="/Public/css/ion.rangeSlider.min.css" />
<script type="text/javascript">
$(function(){
	function isIE() {
			if (!!window.ActiveXObject || "ActiveXObject" in window){
					 return true;
			 }else{
					 return false;
			 }
	 }

	 if(isIE()){
		$('.hang2').css({'margin-top':'10px'});
		$('.length').css({'top':'0'});
	 }else{
		$('.hang1').css({'margin-top':'-20px'});

	 }
});
</script>
</head>
<body class="bg_f2">
<div class="bg_f min_w">
	<div class="m_auto padt_15 clearfix ">
		<div class="fl "><a href="<?php echo U('Index/index');?>"><img src="/Public/images/logo.png" class="dis_b" style="height:78px;" /></a></div>
		<div class="fl ml_50 pos_rela"  >
			<div style="position: absolute;top:55px;left:0;" class="dis_n" id="pl_box">
				<textarea style="width:400px;height: 150px;" id="pl_text" placeholder="每行输入一个批次/捆号" class="border1 dis_b pad_10"></textarea>
				<input type="button" id="pl_sou" value="搜索" class="dis_b bg_ora" style="color: #fff;padding:5px 10px;border:0" />
			</div>
			<form action="<?php echo U('Chaoshi/index');?>" id="ss_fm"  style="margin-top:9px;">
					<input type="search" name="sk" id="sousuo_sk" class="clr_9" value="<?php echo ($_GET['sk']); ?>" autocomplete="off" 
						placeholder="批号/轧花厂/仓库/产棉地区" style="height: 40px;border:2px solid #28afe5;width:500px;padding:10px;box-sizing: border-box;">
					<button type="submit" class="send" style="height: 40px;outline: none;cursor: pointer;">
						<img src="/Public/images/searth.png" style="width:20px;">
					</button>
					<span class="f_14 clr_r pointer" id="pl_click" style="position: absolute;top:30px;right:80px;">批量</span>
		
			</form>
		</div>

		<script>
		$('#pl_click').click(function(){
			$('#pl_box').toggle();
		});

			$('#pl_sou').click(function(){
				var ss = $('#pl_text').val().trim();
				var arr = ss.split(/\n/g);
				var xx = [];
				for(var i = 0; i<arr.length; i++){
					if(arr[i].trim()!=''){
						xx.push(arr[i].trim());
					}
				}
				// console.log(xx.toString());
				$('#sousuo_sk').val(xx.toString());
				$('#ss_fm').submit();
			});
		</script>


		<div class="fr al_rt f_14 login ">
			<?php if($session_user["id"] > 0): ?><div class="mt_10">
					<a href="<?php echo U('Center/info');?>" class="clr_b f_16 log_a">会员中心</a>
					<a href="<?php echo U('Passport/exits');?>" class="clr_b f_16">退出</a>
				</div>
			<?php else: ?>
				<div class="mt_10">
					<a href="<?php echo U('Passport/reg');?>" class="clr_d f_16">注册</a>
					<a href="<?php echo U('Passport/login');?>" class="clr_d f_16 log_a">登录</a>
				</div><?php endif; ?>
			
		</div>
	</div>
</div>


<div class="bg_b min_w">
	<div class="m_auto clearfix">
		<ul class="nav_ul clearfix f_16  fl">
			<li><a id="n1" href="<?php echo U('Index/index');?>" class="clr_f">每日精选</a></li>
			<li><a id="n2"  href="<?php echo U('Chaoshi/index');?>" class="clr_f">棉花商城</a></li>
			<li><a id="n3" href="<?php echo U('Center/dingzhi');?>" class="clr_f">个性需求</a></li>
 			<li><a id="n4" href="<?php echo U('Center/favor');?>" class="clr_f">我的收藏</a></li>
			<!--<li><a id="n5" href="<?php echo U('About/contact');?>" class="clr_f">联系我们</a></li> -->
		</ul>

		<div class="f_16 clr_f nav_time fr">欢迎您，今天是：<?php echo(date('Y年m月d日')); ?></div>
	</div>
</div>

<script type="text/javascript">
	$(function(){
		var h = location.href;
		if(h.indexOf('Chaoshi')>-1){
			$('#n2').addClass('nav_cur');
		}else if(h.indexOf('dingzhi')>-1){
			$('#n3').addClass('nav_cur');
		}else if(h.indexOf('favor')>-1){
			$('#n4').addClass('nav_cur');
			$('#left_3').addClass('nav_cur');
		}else if(h.indexOf('contact')>-1){
			$('#n5').addClass('nav_cur');
		}else if(h.indexOf('info')>-1){
			$('#left_1').addClass('nav_cur');
		}else {
			$('#n1').addClass('nav_cur');
		}
	});
</script>
	<div class="m_auto">
		<div class="bar">棉花商城</div>
		<div class="border1 pad_20 bg_f mt_10">
			<ul class="mian_ul clearfix al_ct f_18">
				<?php if(is_array($chandi)): foreach($chandi as $k=>$v): if($def == $v['id']): ?><li><a href="<?php echo U('index',['def'=>$v['id']]);?>" class="clr_3 mian_cur"><?php echo ($v["name"]); ?></a></li>
					<?php else: ?>
						<li><a href="<?php echo U('index',['def'=>$v['id']]);?>" class="clr_3 mian_norm"><?php echo ($v["name"]); ?></a></li><?php endif; endforeach; endif; ?>
			</ul>
			<ul class="chandi_ul f_14 mt_20 fl" style="line-height:30px;margin-left:5px;width:370px;">
				<li  style="display: inline-block;" class="ver_top">产　地：</li>
				<li  style="display: inline-block;"  >
					<div id="chandi_click">
						<?php if(is_array($chandi_default)): foreach($chandi_default as $key=>$v): if(strpos($_GET['cd'], '|'.$v['id'].'-|') !== false): ?><a href="#." class="clr_3 mr_10 xuan_cur" myid="<?php echo ($v["id"]); ?>"><?php echo ($v["name"]); ?></a>
							<?php else: ?>
								<a href="#." class="clr_3 mr_10 " myid="<?php echo ($v["id"]); ?>"><?php echo ($v["name"]); ?></a><?php endif; endforeach; endif; ?>
					</div>
				</li>
			</ul>
			<ul class="chandi_ul f_14 mt_20 fl "  id="leixing_ul" style="line-height:30px; margin-left:5px;width:375px;">
				<li>类　型：</li>
				<?php if(is_array($xls_leixing)): foreach($xls_leixing as $key=>$v): if(strpos($_GET['leixing'], '|'.$v.'|') !== false): ?><li><a href="#." class="clr_3 xuan_cur"><?php echo ($v); ?></a></li>
					<?php else: ?>
						<li><a href="#." class="clr_3"><?php echo ($v); ?></a></li><?php endif; endforeach; endif; ?>
			</ul>
			<ul class="chandi_ul f_14 mt_20 fl " id="niandu_ul" style="line-height:30px; margin-left:5px; width:330px;">
				<li>年　度：</li>
				<?php if(is_array($xls_niandu)): foreach($xls_niandu as $key=>$v): if(strpos($_GET['pihao_kunkao'], '|'.$v.'|') !== false): ?><li><a href="#." class="clr_3 xuan_cur"><?php echo ($v); ?></a></li>
					<?php else: ?>
						<li><a href="#." class="clr_3"><?php echo ($v); ?></a></li><?php endif; endforeach; endif; ?>
			</ul>
			<div class="cls"></div>
			<div id="child_wraper">
				<?php if(is_array($chandi_default)): foreach($chandi_default as $key=>$v): ?><span id="child_<?php echo ($v["id"]); ?>">
						<?php if($_GET['cd2'] != '' and $_GET['cd2'] != '|'): if(is_array($cd2[$v['id']])): foreach($cd2[$v['id']] as $key=>$v2): if(strpos($_GET['cd2'], '|'.$v['id'].'-'.$v2['id'].'-|') !== false): ?><a href="#." class="clr_3 mr_10 xuan_cur f_14" pid="<?php echo ($v["id"]); ?>" myid="<?php echo ($v2["id"]); ?>"><?php echo ($v2["name"]); ?></a>
								<?php else: ?>
									<a href="#." class="clr_3 mr_10 f_14" pid="<?php echo ($v["id"]); ?>" myid="<?php echo ($v2["id"]); ?>"><?php echo ($v2["name"]); ?></a><?php endif; endforeach; endif; endif; ?>
					</span><?php endforeach; endif; ?>
			</div>
			<div id="grandson_wraper" class="mt_10">
				<?php if(is_array($xx)): foreach($xx as $k=>$v): if(is_array($v)): foreach($v as $key=>$v2): ?><span id="grandson_<?php echo ($v2["id"]); ?>" gpid="<?php echo ($k); ?>" pid="<?php echo ($v2["id"]); ?>">
							<?php if($_GET['cd3'] != '' and $_GET['cd3'] != '|' and strpos($_GET['cd2'], '|'.$k.'-'.$v2['id'].'-|') !== false): if(is_array($v2["children"])): foreach($v2["children"] as $key=>$v3): if(strpos($_GET['cd3'], '|'.$k.'-'.$v3['pid'].'-'.$v3['id'].'-|') !== false): ?><a href="#." gpid="<?php echo ($k); ?>" pid="<?php echo ($v3["pid"]); ?>" myid="<?php echo ($v3["id"]); ?>" class="mr_10 clr_3 f_14 xuan_cur"><?php echo ($v3["name"]); ?></a>
									<?php else: ?>
										<a href="#." gpid="<?php echo ($k); ?>" pid="<?php echo ($v3["pid"]); ?>" myid="<?php echo ($v3["id"]); ?>" class="mr_10 clr_3 f_14"><?php echo ($v3["name"]); ?></a><?php endif; endforeach; endif; endif; ?>
						</span><?php endforeach; endif; endforeach; endif; ?>
			</div>
			<script type="text/javascript">
				//点击子产地
				$('#child_wraper').on('click', 'a', function(){
					var that  = $(this);
					if(!that.hasClass('xuan_cur')){
						that.addClass('xuan_cur');
					}else{
						that.removeClass('xuan_cur');
					}
				});
				//点击父产地
				$('#chandi_click').on('click', 'a', function(){
					var that  = $(this);
					if(!that.hasClass('xuan_cur')){
						$.getJSON('<?php echo U("Tools/getCat");?>', {id:that.attr('myid')}, function(d){
							// console.log(d)
							$.each(d, function(k,v){
								$('#child_'+that.attr('myid')).append('<a href="#." pid="'+v.pid+'" myid="'+v.id+'" class="mr_10 clr_3 f_14">'+v.name+'</a>');
								$('#grandson_wraper').append('<span id="grandson_'+v.id+'" gpid="'+v.pid+'"></span>')
							});
							that.addClass('xuan_cur');
						});
					}else{
						that.removeClass('xuan_cur');
					}
					$('#child_'+that.attr('myid')+' a[pid="'+that.attr('myid')+'"]').remove();
					$('#grandson_wraper a[gpid="'+that.attr('myid')+'"]').remove();
					//删除对应二级的占位
					$('#grandson_wraper span[gpid='+that.attr('myid')+']').remove();
				});
			</script>
			<script type="text/javascript">
				var leixing_str= '<?php echo ($_GET["leixing"]); ?>' == '' ? '|' : '<?php echo ($_GET["leixing"]); ?>' ;
				// console.log(leixing_str)
				$('#leixing_ul li a').click(function(){
					if(!$(this).hasClass('xuan_cur')){
						$(this).addClass('xuan_cur');
						if(leixing_str.indexOf('|'+$(this).text()+'|') ==-1){
							leixing_str += $(this).text()+'|';
						}
					}else{
						$(this).removeClass('xuan_cur');
						leixing_str = leixing_str.replace('|'+$(this).text()+'|', '|');
					}
					$('input[name="leixing"]').val(leixing_str);
				});
			</script>
			<script type="text/javascript">
				var niandu_str=  '<?php echo ($_GET["pihao_kunkao"]); ?>' == '' ? '|' : '<?php echo ($_GET["pihao_kunkao"]); ?>' ;
				$('#niandu_ul li a').click(function(){
					if(!$(this).hasClass('xuan_cur')){
						$(this).addClass('xuan_cur');
						if(niandu_str.indexOf('|'+$(this).text()+'|') ==-1){
							niandu_str += $(this).text()+'|';
						}
					}else{
						$(this).removeClass('xuan_cur');
						niandu_str = niandu_str.replace('|'+$(this).text()+'|', '|');
					}
					 $('input[name="pihao_kunkao"]').val(niandu_str);
				});
			</script>
			<div class="cls"></div>
			<ul class="chandi_ul f_14  fl hang1" style="width:380px;">
				<li class="length">长　度</li>
				<li style="width:240px;"><input type="text" class="changdu_range" name="my_range" value="" style="cursor: pointer"  /></li>
			</ul>
			<script>
				$(".changdu_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 25,
						max: 32,
						from: '<?php echo ($_GET["changdu_from"]); ?>' == '' ? 25 : '<?php echo ($_GET["changdu_from"]); ?>',
						to: '<?php echo ($_GET["changdu_to"]); ?>' == '' ? 32 : '<?php echo ($_GET["changdu_to"]); ?>',
						step:0.1,
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
							 $('input[name="changdu_from"]').val(data.from);
							 $('input[name="changdu_to"]').val(data.to);
						}
				});
			</script>
			<ul class="chandi_ul f_14 fl hang1" style="width:380px;">
				<li  class="length">强　力</li>
				<li style="width:240px;"><input type="text" class="qiangli_range" name="my_range" value=""   /></li>
			</ul>
			<script>
				$(".qiangli_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 25,
						max: 32,
						from: '<?php echo ($_GET["qiangli_from"]); ?>' == '' ? 25 : '<?php echo ($_GET["qiangli_from"]); ?>',
						to: '<?php echo ($_GET["qiangli_to"]); ?>' == '' ? 32 : '<?php echo ($_GET["qiangli_to"]); ?>',
						step:0.1,
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
								$('input[name="qiangli_from"]').val(data.from);
							 $('input[name="qiangli_to"]').val(data.to);
						}
				});
			</script>
			<ul class="chandi_ul f_14  fl hang1"  style="width:380px;">
				<li class="length">马　值</li>
				<li style="width:240px;"><input type="text" class="mazhi_range" name="my_range" value=""   /></li>
			</ul>
			<script>
				$(".mazhi_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 2.5,
						max: 5.5,
						from: '<?php echo ($_GET["mazhi_from"]); ?>' == '' ? 2.5 : '<?php echo ($_GET["mazhi_from"]); ?>',
						to: '<?php echo ($_GET["mazhi_to"]); ?>' == '' ? 5.5 : '<?php echo ($_GET["mazhi_to"]); ?>',
						step:0.1,
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
							 $('input[name="mazhi_from"]').val(data.from);
							 $('input[name="mazhi_to"]').val(data.to);
						}
				});
			</script>
			<div class="cls"></div>
			<ul class="chandi_ul f_14  fl hang2" style="width:380px;">
				<li class="length">回　潮</li>
				<li style="width:240px;"><input type="text" class="huichao_range" name="my_range" value=""   /></li>
			</ul>
			<script>
				$(".huichao_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 0,
						max: 10,
						from: '<?php echo ($_GET["huichaolv_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["huichaolv_from"]); ?>',
						to: '<?php echo ($_GET["huichaolv_to"]); ?>' == '' ? 10 : '<?php echo ($_GET["huichaolv_to"]); ?>',
						step:0.1,
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
							 $('input[name="huichaolv_from"]').val(data.from);
							 $('input[name="huichaolv_to"]').val(data.to);
						}
				});
			</script>
			<ul class="chandi_ul f_14  fl hang2"  style="width:380px;">
				<li class="length">整齐度</li>
				<li style="width:240px;"><input type="text" class="zhengqidu_range" name="my_range" value=""   /></li>
			</ul>
			<script>
				$(".zhengqidu_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 77,
						max: 90,
						from: '<?php echo ($_GET["zhengqidu_from"]); ?>' == '' ? 77 : '<?php echo ($_GET["zhengqidu_from"]); ?>',
						to: '<?php echo ($_GET["zhengqidu_to"]); ?>' == '' ? 90 : '<?php echo ($_GET["zhengqidu_to"]); ?>',
						step:0.1,
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
							 $('input[name="zhengqidu_from"]').val(data.from);
							 $('input[name="zhengqidu_to"]').val(data.to);
						}
				});
			</script>
			<ul class="chandi_ul f_14  fl hang2" style="width:380px;">
				<li class="length">含　杂</li>
				<li style="width:240px;"><input type="text" class="hanza_range" name="my_range" value=""   /></li>
			</ul>
			<script>
				$(".hanza_range").ionRangeSlider({
					type: "double",
					skin:"round",
						type: "double",
						min: 0,
						max: 6,
						from: '<?php echo ($_GET["hanzalv_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["hanzalv_from"]); ?>',
						to: '<?php echo ($_GET["hanzalv_to"]); ?>' == '' ? 6 : '<?php echo ($_GET["hanzalv_to"]); ?>',
						step:0.1,
						onFinish: function (data) { //拖动结束回调
							 // console.log(data)
								$('input[name="hanzalv_from"]').val(data.from);
							 $('input[name="hanzalv_to"]').val(data.to);
						}
				});
			</script>
			<div class="cls"></div>
			<div class="bottom">
				<ul class="chandi_ul f_14  fl hang2" style="width:380px;">
					<li class="green length">白棉1/2/3级</li>
					<li style="width:240px;">
						<input type="text" class="bm123_range" name="bm123_range" value=""   />
					</li>
				</ul>
				<script>
					$(".bm123_range").ionRangeSlider({
						type: "double",
						skin:"round",
							type: "double",
							min: 0,
							max: 100,
							from: '<?php echo ($_GET["bm123_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["bm123_from"]); ?>',
							to:'<?php echo ($_GET["bm123_to"]); ?>' == '' ? 100 : '<?php echo ($_GET["bm123_to"]); ?>',
							step:0.1,
							onFinish: function (data) { //拖动结束回调
								 // console.log(data)
								 $('input[name="bm123_from"]').val(data.from);
								 $('input[name="bm123_to"]').val(data.to);
							}
					});
				</script>
				<ul class="chandi_ul f_14  fl hang2" style="width:380px;">
					<li class="green length">白棉4/5级</li>
					<li style="width:240px;"><input type="text" class="bm45_range" name="bm45_range" value=""   /></li>
				</ul>
				<script>
					$(".bm45_range").ionRangeSlider({
						type: "double",
						skin:"round",
							type: "double",
							min: 0,
							max: 100,
							from: '<?php echo ($_GET["bm45_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["bm45_from"]); ?>',
							to: '<?php echo ($_GET["bm45_to"]); ?>' == '' ? 100 : '<?php echo ($_GET["bm45_to"]); ?>',
							step:0.1,
							onFinish: function (data) { //拖动结束回调
								 // console.log(data)
									$('input[name="bm45_from"]').val(data.from);
								 $('input[name="bm45_to"]').val(data.to);
							}
					});
				</script>
				<ul class="chandi_ul f_14  fl hang2"  style="width:380px;">
					<li class="green length">淡点污1/2/3级</li>
					<li style="width:240px;">
						<input type="text" class="ddw123_range" name="ddw123_range" value=""   />
					</li>
				</ul>
				<script>
					$(".ddw123_range").ionRangeSlider({
						type: "double",
						skin:"round",
							type: "double",
							min: 0,
							max: 100,
							from: '<?php echo ($_GET["ddw123_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["ddw123_from"]); ?>',
							to: '<?php echo ($_GET["ddw123_to"]); ?>' == '' ? 100 : '<?php echo ($_GET["ddw123_to"]); ?>',
							step:0.1,
							onFinish: function (data) { //拖动结束回调
								 // console.log(data)
								 $('input[name="ddw123_from"]').val(data.from);
								 $('input[name="ddw123_to"]').val(data.to);
							}
					});
				</script>
				<div class="cls"></div>
				<ul class="chandi_ul f_14  fl hang2" style="width:380px;">
					<li class="green length">淡黄染1/2/3级</li>
					<li style="width:240px;">
						<input type="text" class="dhr123_range" name="dhr123_range" value=""   />
					</li>
				</ul>
				<script>
					$(".dhr123_range").ionRangeSlider({
						type: "double",
						skin:"round",
							type: "double",
							min: 0,
							max: 100,
							from: '<?php echo ($_GET["dhr123_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["dhr123_from"]); ?>',
							to: '<?php echo ($_GET["dhr123_to"]); ?>' == '' ? 100 : '<?php echo ($_GET["dhr123_to"]); ?>',
							step:0.1,
							onFinish: function (data) { //拖动结束回调
								 // console.log(data)
								 $('input[name="dhr123_from"]').val(data.from);
								 $('input[name="dhr123_to"]').val(data.to);
							}
					});
				</script>
				<ul class="chandi_ul f_14  fl hang2" style="width:380px;">
					<li class="green length">黄染1/2级</li>
					<li style="width:240px;"><input type="text" class="hr12_range" name="hr12_range" value=""   /></li>
				</ul>
				<script>
					$(".hr12_range").ionRangeSlider({
						type: "double",
						skin:"round",
							type: "double",
							min: 0,
							max: 100,
							from: '<?php echo ($_GET["hr12_from"]); ?>' == '' ? 0 : '<?php echo ($_GET["hr12_from"]); ?>',
							to: '<?php echo ($_GET["hr12_to"]); ?>' == '' ? 100 : '<?php echo ($_GET["hr12_to"]); ?>',
							step:0.1,
							onFinish: function (data) { //拖动结束回调
								 // console.log(data)
									$('input[name="hr12_from"]').val(data.from);
								 $('input[name="hr12_to"]').val(data.to);
							}
					});
				</script>
				<div class="cls"></div>
			</div>
			<ul class="chandi_ul f_14 mt_30 " id="jiaohuodi_ul" style="width:760px;">
				<li>交货地：</li>
				<li >
					<div id="jhd_click">
						<?php if(is_array($jiaohuodi)): foreach($jiaohuodi as $k=>$v): if(strpos($_GET['jhd'], '|'.$v['id'].'-|') !== false): ?><a href="#." class="clr_3 mr_10 xuan_cur" myid="<?php echo ($v["id"]); ?>"><?php echo ($v["name"]); ?></a>
							<?php else: ?>
								<a href="#." class="clr_3 mr_10 " myid="<?php echo ($v["id"]); ?>"><?php echo ($v["name"]); ?></a><?php endif; endforeach; endif; ?>
					</div>
				</li>
			</ul>
			<div id="jhd_wraper" class="mt_10">
				<?php if(is_array($jiaohuodi)): foreach($jiaohuodi as $key=>$v): ?><span id="jhd_<?php echo ($v["id"]); ?>">
						<?php if($_GET['jhd2'] != '' and $_GET['jhd2'] != '|'): if(is_array($jhd2[$v['id']])): foreach($jhd2[$v['id']] as $key=>$v2): if(strpos($_GET['jhd2'], '|'.$v['id'].'-'.$v2['id'].'-|') !== false): ?><a href="#." class="clr_3 mr_10 xuan_cur f_14" pid="<?php echo ($v["id"]); ?>" myid="<?php echo ($v2["id"]); ?>"><?php echo ($v2["name"]); ?></a>
								<?php else: ?>
									<a href="#." class="clr_3 mr_10 f_14" pid="<?php echo ($v["id"]); ?>" myid="<?php echo ($v2["id"]); ?>"><?php echo ($v2["name"]); ?></a><?php endif; endforeach; endif; endif; ?>
					</span><?php endforeach; endif; ?>
			</div>
			<script type="text/javascript">
			//点击父交货地
				$('#jhd_click').on('click', 'a', function(){
					var that  = $(this);
					if(!that.hasClass('xuan_cur')){
						$.getJSON('<?php echo U("Tools/getJhd");?>', {id:that.attr('myid')}, function(d){
							// console.log(d)
							$.each(d, function(k,v){
								$('#jhd_'+that.attr('myid')).append('<a href="#." pid="'+v.pid+'" myid="'+v.id+'" class="mr_10 clr_3 f_14">'+v.name+'</a>');
							});
							that.addClass('xuan_cur');
						});
					}else{
						that.removeClass('xuan_cur');
					}
					$('#jhd_'+that.attr('myid')+' a[pid="'+that.attr('myid')+'"]').remove();
				});
				//点击子交货地
				$('#jhd_wraper').on('click', 'a', function(){
					var that  = $(this);
					if(!that.hasClass('xuan_cur')){
						that.addClass('xuan_cur');
					}else{
						that.removeClass('xuan_cur');
					}
				});
			</script>
			<form action="<?php echo U('index');?>" id="fm1">
				<input type="hidden" name="sk" value="<?php echo ($_GET['sk']); ?>" />
				<input id="export_count" name="export_count" type="hidden">
				<input id="act" name="act" value="" type="hidden" />
				<div class="dis_n">
					<input name="def" value="<?php echo ($def); ?>" />
					<!-- 父产地 -->
					<input name="cd" value="" />
					<!-- 子产地 -->
					<input name="cd2" value="" />
					<!-- 孙产地 -->
					<input name="cd3" value="" />
					<!-- 父交货地-->
					<input name="jhd" value="" placeholder="jhd" />
					<!-- 子交货地 -->
					<input name="jhd2" value=""  placeholder="jhd2"/>
					<textarea id="url" name="url"></textarea>
					<input name="leixing" value="<?php echo ($_GET['leixing']); ?>" />
					<input name="pihao_kunkao" value="<?php echo ($_GET['pihao_kunkao']); ?>" />
					<input name="changdu_from" value="<?php echo ($_GET['changdu_from'] == '' ? 25 : $_GET['changdu_from']); ?>" />
					<input name="changdu_to" value="<?php echo ($_GET['changdu_to'] == '' ? 32 : $_GET['changdu_to']); ?>"/>
					<input name="qiangli_from" value="<?php echo ($_GET['qiangli_from'] == '' ? 25 : $_GET['qiangli_from']); ?>"/>
					<input name="qiangli_to" value="<?php echo ($_GET['qiangli_to'] == '' ? 32 : $_GET['qiangli_to']); ?>"/>
					<input name="mazhi_from" value="<?php if($_GET['mazhi_from'] == ''){echo (2.5);}else{echo ($_GET['mazhi_from']);} ?>"/>
					<input name="mazhi_to" value="<?php if($_GET['mazhi_to'] == ''){echo (5.5);}else{echo ($_GET['mazhi_to']);} ?>"/>
					<input name="huichaolv_from" value="<?php echo ($_GET['huichaolv_from'] == '' ? 0 : $_GET['huichaolv_from']); ?>"/>
					<input name="huichaolv_to" value="<?php echo ($_GET['huichaolv_to'] == '' ? 10 : $_GET['huichaolv_to']); ?>"/>
					<input name="hanzalv_from" value="<?php echo ($_GET['hanzalv_from'] == '' ? 0 : $_GET['hanzalv_from']); ?>"/>
					<input name="hanzalv_to" value="<?php echo ($_GET['hanzalv_to'] == '' ? 6 : $_GET['hanzalv_to']); ?>"/>
					<input name="zhengqidu_from" value="<?php echo ($_GET['zhengqidu_from'] == '' ? 77 : $_GET['zhengqidu_from']); ?>"/>
					<input name="zhengqidu_to" value="<?php echo ($_GET['zhengqidu_to'] == '' ? 90 : $_GET['zhengqidu_to']); ?>"/>
					<input name="bm123_from" value="<?php echo ($_GET['bm123_from'] == '' ? 0 : $_GET['bm123_from']); ?>"/>
					<input name="bm123_to" value="<?php echo ($_GET['bm123_to'] == '' ? 100 : $_GET['bm123_to']); ?>"/>
					<input name="bm45_from" value="<?php echo ($_GET['bm45_from'] == '' ? 0 : $_GET['bm45_from']); ?>"/>
					<input name="bm45_to" value="<?php echo ($_GET['bm45_to'] == '' ? 100 : $_GET['bm45_to']); ?>"/>
					<input name="ddw123_from" value="<?php echo ($_GET['ddw123_from'] == '' ? 0 : $_GET['ddw123_from']); ?>"/>
					<input name="ddw123_to" value="<?php echo ($_GET['ddw123_to'] == '' ? 100 : $_GET['ddw123_to']); ?>"/>
					<input name="dhr123_from" value="<?php echo ($_GET['dhr123_from'] == '' ? 0 : $_GET['dhr123_from']); ?>"/>
					<input name="dhr123_to" value="<?php echo ($_GET['dhr123_to'] == '' ? 100 : $_GET['dhr123_to']); ?>"/>
					<input name="hr12_from" value="<?php echo ($_GET['hr12_from'] == '' ? 0 : $_GET['hr12_from']); ?>"/>
					<input name="hr12_to" value="<?php echo ($_GET['hr12_to'] == '' ? 100 : $_GET['hr12_to']); ?>"/>
				</div>
				<ul class="chandi_ul f_14 mt_10">
					<li>
						交货仓库：<input class="border1 f_14 clr_9 pad_5" placeholder="请输入仓库名称"
							value="<?php echo ($_GET['cangchumingcheng']); ?>" name="cangchumingcheng" style="width:350px;" />
						轧花厂：<input class="border1 f_14 clr_9 pad_5" placeholder="请输入轧花厂名称或编号"
							value="<?php echo ($_GET['jiagongchang']); ?>" name="jiagongchang"  style="width:350px;" />
						<input type="button" id="tijiaoClick" value="✓ 确认筛选　" class="f_14 clr_f bg_lan border0 pad_5" />
						<a href="#." id="add_dingzhi" style=" font-weight: 700;
						color: #F1830E;
						padding-left: 10px;
						font-size: 15px;">+添加到个性需求</a>
						<!-- <a href="#" class="clr_ora f_wei">+添加到需求定制</a> -->
					</li>
				</ul>
				<div class="dis_n">
					<input name="hot" value="<?php echo ($_GET['hot']); ?>" />
					<input name="heyue" value="<?php echo ($_GET['heyue']); ?>" />
					<input name="zuiyou" value="<?php echo ($_GET['zuiyou']); ?>" />
					<input name="xiangtong"  value="<?php echo ($_GET['xiangtong']); ?>" />
					<input name="dan"  value="<?php echo ($_GET['dan']); ?>" />
					<input name="jicha"  value="<?php echo ($_GET['jicha']); ?>" />
					<input name="premium_sort" value="<?php echo ($_GET['premium_sort']); ?>" />
				</div>
			</form>
			<script type="text/javascript">
				$('#add_dingzhi').click(function(){
					setForm();
					$('#url').val($('#fm1').serialize());
					$.post('<?php echo U("rDingzhi");?>',$('#fm1').serialize(), function(d){
						alert(d.msg);
						if(d.code==1){
							location.href="<?php echo U('Center/dingzhi');?>";
						}
					},'json');
				});
				function setForm(){
					//提交表单，而不是导出excel
					$('#act').val('');
					//所选的父产地
					var cd = '|';
					$('#chandi_click a').each(function(k, v){
						if($(this).hasClass('xuan_cur')){
							// cd += $(this).text() + '|';
							// console.log('v----'+$(this).attr('myid'));
							cd += $(this).attr('myid') + '-|';
						}
					});
					$('input[name="cd"]').val(cd);
					//所选的子产地
					var cd2 = '|';
					$('#child_wraper a').each(function(k, v){
						if($(this).hasClass('xuan_cur')){
							// cd2 += $(this).text() + '|';
							// console.log('v----'+$(this).attr('myid'));
							cd2 += $(this).attr('pid')+'-'+$(this).attr('myid') + '-|';
						}
					});
					$('input[name="cd2"]').val(cd2);
					//所选的孙产地
					var cd3 = '|';
					$('#grandson_wraper a').each(function(k, v){
						if($(this).hasClass('xuan_cur')){
							// cd3+= $(this).text() + '|';
							// console.log('v----'+$(this).attr('myid'));
							cd3+= $(this).attr('gpid')+'-'+$(this).attr('pid')+'-'+$(this).attr('myid')+ '-|';
						}
					});
					$('input[name="cd3"]').val(cd3);
					//所选的父交货地
					var jhd = '|';
					$('#jhd_click a').each(function(k, v){
						if($(this).hasClass('xuan_cur')){
							// cd += $(this).text() + '|';
							// console.log('v----'+$(this).attr('myid'));
							jhd += $(this).attr('myid') + '-|';
						}
					});
					$('input[name="jhd"]').val(jhd);
					//所选的子交货地
					var jhd2 = '|';
					$('#jhd_wraper a').each(function(k, v){
						if($(this).hasClass('xuan_cur')){
							// cd2 += $(this).text() + '|';
							// console.log('v----'+$(this).attr('myid'));
							jhd2 += $(this).attr('pid')+'-'+$(this).attr('myid') + '-|';
						}
					});
					$('input[name="jhd2"]').val(jhd2);
				}
				//提交表单
				$('#tijiaoClick').click(function(){
					setForm();
					//合约筛选置空
					$('input[name="heyue"]').val('');
					$('#fm1').submit();
				});
			</script>
		</div>
		<div class="border1 bg_f mt_10 pad_10">
			<ul class="paixu_ul f_14 clearfix">
				<li class="clr_b f_wei">综合排序</li>
<!-- 				<li id="zuiyou_order">
					<label class="mr_5" v="20">
						<input type="checkbox" class="ver_mid" <?php if($_GET['zuiyou'] == 20): ?>checked<?php endif; ?>  />
						<span class="ver_mid">前20批</span>
					</label>
					<label class="mr_5" v="50">
						<input type="checkbox" class="ver_mid"  <?php if($_GET['zuiyou'] == 50): ?>checked<?php endif; ?> />
						<span class="ver_mid">前50批</span>
					</label>
				</li>
 -->
				<li id="hot_order" style="margin-right: 15px;">
					<label class="mr_5 rad5" style="background: #e8e3df;padding:1px 5px 3px 5px;" >
						<input type="checkbox" class="ver_mid" <?php if($_GET['hot'] == 1): ?>checked<?php endif; ?>  />
						<span class="ver_mid">热门</span>
					</label>
				</li>
				<li  style="border:0;">
					<input id="export_count_biao" placeholder="导出条数" value="300" onkeyup="this.value=this.value.replace(/[^0-9-]+/,'');"
						class="ver_mid border1" style="width:60px;" />
					<a href="#." id="excelClick"><img src="/Public/images/excel.png" class="dis_ib ver_mid" /> <span class="ver_mid" style="color:#217346;">导出报价</span></a>
				</li>
				<li style="border:0;" class="f_14 clr_9" id="process"></li>
				<div class="fr" style="border:1px solid #fff;">
					<li id="xunjia_order"  style="border:0;">
						<label class="mr_5" v="询价">
							<input type="checkbox" class="ver_mid" <?php if($_GET['heyue'] == '询价'): ?>checked<?php endif; ?>/> <span class="ver_mid">询价</span>
						</label>
					</li>

					<li id="dabao_order"  style="border:0;">
						<label class="mr_5" v="打包">
							<input type="checkbox" class="ver_mid" <?php if($_GET['heyue'] == '打包'): ?>checked<?php endif; ?>/> <span class="ver_mid">打包</span>
						</label>
					</li>

					<li id="jicha_order"  style="border:0;">
						<label class="mr_5" v="jicha">
							<input type="checkbox" class="ver_mid" <?php if($_GET['heyue'] == 'jicha'): ?>checked<?php endif; ?>/> <span class="ver_mid">基差点价</span>
						</label>
					</li>
					<li id="premium_sort_order" style="border:0;">
						<label class="mr_5" v="premium_sort">
							<input type="checkbox" class="ver_mid" <?php if($_GET['premium_sort'] == '1'): ?>checked<?php endif; ?>/> <span class="ver_mid">升贴水排序</span>
						</label>
					</li>
					<li id="heyue_order"  style="border:0;">
						<label class="mr_5" v="一口价">
							<input type="checkbox" class="ver_mid" <?php if($_GET['heyue'] == '一口价'): ?>checked<?php endif; ?>/> <span class="ver_mid">一口价</span>
						</label>
						<label class="mr_5" v="<?php echo ($setting['heyue05']['value']); ?>">
							<input type="checkbox" class="ver_mid" <?php if($_GET['heyue'] == $setting['heyue05']['value']): ?>checked<?php endif; ?>/>
							<span class="ver_mid"><?php echo ($setting['heyue05_text']['value']); ?></span>
						</label>
						<label class="mr_5" v="<?php echo ($setting['heyue09']['value']); ?>">
							<input type="checkbox" class="ver_mid" <?php if($_GET['heyue'] == $setting['heyue09']['value']): ?>checked<?php endif; ?>/>
							<span class="ver_mid"><?php echo ($setting['heyue09_text']['value']); ?></span>
						</label>

						<label class="mr_5" v="<?php echo ($setting['heyue2405']['value']); ?>">
							<input type="checkbox" class="ver_mid" <?php if($_GET['heyue'] == $setting['heyue2405']['value']): ?>checked<?php endif; ?>/>
							<span class="ver_mid"><?php echo ($setting['heyue2405_text']['value']); ?></span>
						</label>

						<span class="ver_mid">共计<?php echo ($count); ?>批</span>
					</li>
				</div>
			</ul>
<div class="mask_bg dis_n" id="mask_bg" >
	<div class="bg_f pad_40" style="width:500px;margin:200px auto ; border-radius: 10px;">
		<div  id="mask_tips"><span class="ver_mid">正在导出数据，请耐心等待</span>
			<img src="/Public/images/loading.gif" style="width:50px;" class="ver_mid" />
		</div>
	</div>
</div>
			<script type="text/javascript">
			$('#close_mask').click(function(){
				$('#mask_bg').hide();
			});
			var sess = '<?php echo ($session_user["id"]); ?>';
			var sess_type = '<?php echo ($session_user["member_type"]); ?>';
			$('#excelClick').click(function(){
				if(sess==''){
					alert('请登录');
					return false;
				}
				if($("#export_count_biao").val()==''){
					alert('请输入导出条数');
					return false;
				}
				if(sess_type == 1){
					if($("#export_count_biao").val() > 45000){
						alert('最大不超过45000条');
						return false;
					}
				}else{
					if($("#export_count_biao").val() > 300){
						alert('普通会员不超过300条');
						return false;
					}
				}
				$('#mask_bg').show();
				setForm();
				$('#act').val('excel');
				$('#export_count').val($("#export_count_biao").val());
				$.getJSON('<?php echo U("index");?>', $('#fm1').serialize(), function(d){
					console.log(d)
					if(d.code==1){
						$('#mask_bg').hide();
						location.href=d.url;
					}else{
						alert(d.msg);
					}
				 });
				 // $('#fm1').submit();
			});
				$('#hot_order label').click(function(){
					setForm();
					if($(this).find('input[type="checkbox"]').is(':checked')){
						$('input[name="hot"]').val(1);
					}else{
						$('input[name="hot"]').val(0);
					}
					$('#fm1').submit();
				});
				$('#xunjia_order label').click(function(){
					setForm();
					if($(this).find('input[type="checkbox"]').is(':checked')){
						$('input[name="heyue"]').val($(this).attr('v'));
					}else{
						$('input[name="heyue"]').val('');
					}
					$('#fm1').submit();
				});
				$('#dabao_order label').click(function(){
					setForm();
					if($(this).find('input[type="checkbox"]').is(':checked')){
						$('input[name="heyue"]').val($(this).attr('v'));
					}else{
						$('input[name="heyue"]').val('');
					}
					$('#fm1').submit();
				});
				$('#heyue_order label').click(function(){
					setForm();
					if($(this).find('input[type="checkbox"]').is(':checked')){
						$('input[name="heyue"]').val($(this).attr('v'));
					}else{
						$('input[name="heyue"]').val('');
					}
					$('#fm1').submit();
				});
				$('#zuiyou_order label').click(function(){
					setForm();
					if($(this).find('input[type="checkbox"]').is(':checked')){
						$('input[name="zuiyou"]').val($(this).attr('v'));
					}else{
						$('input[name="zuiyou"]').val('');
					}
					$('#fm1').submit();
				});
				$('#jicha_order label').click(function(){
					setForm();
					if($(this).find('input[type="checkbox"]').is(':checked')){
						$('input[name="heyue"]').val($(this).attr('v'));
					}else{
						$('input[name="heyue"]').val('');
					}
					$('#fm1').submit();
				});

				$('#premium_sort_order label').click(function(){
					setForm();
					if($(this).find('input[type="checkbox"]').is(':checked')){
						$('input[name="premium_sort"]').val('1');
					}else{
						$('input[name="premium_sort"]').val('');
					}
					$('#fm1').submit();
				});
			</script>
		</div>
		<?php if(is_array($lists)): foreach($lists as $key=>$v): ?><div class="border1 bg_gr pad_10 f_14 clr_3 clearfix mt_10 pos_rela"  style="margin:10px 0;">
				<div class="fl" style="width:830px; font-size:16px;" >
					<div style=" font-size:16px;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 1;-webkit-box-orient: vertical;">
						<!-- <div class="dis_ib cs1 "><div class="l1  dis_ib">5星</div><div class="l2  dis_ib">卖家</div></div> -->
						<a href="<?php echo U('view',['id'=>$v['id']]);?>" class="clr_b f_wei"><?php echo ($v["pihao_kunkao"]); ?></a>
						<span class="ml_10"><?php echo ($v["leixing"]); ?></span>
						<span class="ml_10">
							<!-- <?php if($v["baoshu"] < 10 or $v["baoshu"] == ''): ?>-<?php else: echo ($v["baoshu"]); endif; ?>件 &nbsp; -->
							<?php echo ($v["baoshu"]); ?>件 &nbsp;
						</span>
										<span class="clr_3"><?php echo ($v["diqu_text"]); ?></span>
						<span class="clr_9">加工厂 <span class="clr_3"><?php echo ($v["jiagongchang"]); ?></span></span>
					</div>
					<div class="lh_30 mt_5" title="<?php echo ($v["yanseji_pinji"]); ?>" style=" font-size:16px;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 1;-webkit-box-orient: vertical;">
						<span class="clr_9">颜色 <span class="clr_3"><?php echo ($v["yanseji_pinji"]); ?></span></span>
					</div>
					<div class=" lh_30 " style="font-size:16px;">
						<span class="clr_9">长度 <span class="clr_3"><?php echo ($v["changdu"]); ?></span></span>
						<span class="clr_9">强力 <span class="clr_3"><?php echo ($v["qiangli"]); ?></span></span>
						<span class="clr_9">马值 <span class="clr_3"><?php echo ($v["mazhi"]); ?></span></span>
						<span class="clr_9">含杂<span class="clr_3"><?php echo ($v["hanzalv"]); ?></span></span>
						<span class="clr_9">回潮 <span class="clr_3"><?php echo ($v["huichaolv"]); ?></span></span>
						<span class="clr_9">整齐度<span class="clr_3"><?php echo ($v["zhengqidu"]); ?></span></span>
						<span class="clr_9">公重<span class="clr_3"><?php echo ($v["gongzhong"]); ?>吨</span></span>
						<span class="clr_9">轧工质量<span class="clr_3"><?php echo ($v["ygzl"]); ?></span></span><br />
					</div>
					<div class="mt_5">
						<span class="clr_9">仓储名称 <span class="clr_3"><?php echo ($v["cangchumingcheng"]); ?></span></span>
						<span class="clr_9" style="margin:0 15px;">
							<span class="clr_3"><?php echo ($v["diqu_text_cangku"]); ?></span>
						</span>
						<span class="clr_9">更新时间： <span class="clr_3"><?php echo (date("Y/m/d H:i", $v["add_time"])); ?></span></span>
					</div>
				</div>
				<div class="fr clearfix f_14 al_ct " style="width:280px;">
					<div class="clr_9 ">参考合约： <span><?php echo ($v["dianjiaheyue"]); ?></span></div>
					<div class="clr_9 pos_rela">
						基差： <span class="clr_3 f_16"><?php echo ($v["jicha"]); ?></span>
						<?php if($session_user['member_type'] == 1): ?><span class="clr_ora f_14" style="position: absolute;top:0;right:10px;"><?php echo ($v["daima_cut"]); ?></span><?php endif; ?>
					</div>
					<div class="clr_9">
						升贴水：
						<?php if($v["premium_discount"] > 0): ?><span class="clr_green f_16">+<?php echo ($v["premium_discount"]); ?></span>
						<?php elseif($v["premium_discount"] < 0): ?>
							<span class="clr_red f_16"><?php echo ($v["premium_discount"]); ?></span>
						<?php else: ?>
							<span class="clr_9 f_16">0</span><?php endif; ?>
					</div>
					<?php if($v["is_favor"] == 1): ?><a href="#." class="f_14 zy_box clr_ora dis_ib  cancel_favor mt_5" myid="<?php echo ($v["id"]); ?>" style="background: #f1830e;color:#fff;">
							<span class="ver_mid">已收藏</span>
						</a>
					<?php else: ?>
						<a href="#." class="f_14 zy_box clr_ora dis_ib  add_favor mt_5" myid="<?php echo ($v["id"]); ?>" >
							<span class="ver_mid">收藏</span>
						</a><?php endif; ?>
				</div>
			</div><?php endforeach; endif; ?>
		<script type="text/javascript">
			$('.cancel_favor').click(function(){
				if(confirm('确认取消收藏？')){
					$.getJSON('<?php echo U("cancelFavor");?>', {id:$(this).attr('myid')}, function(d){
						// alert(d.msg);
						history.go(0);
					});
				}
			});
			$('.add_favor').click(function(){
				$.getJSON('<?php echo U("addFavor");?>', {id:$(this).attr('myid')}, function(d){
					alert(d.msg);
					history.go(0);
				});
			});
		</script>
		<div class="pager mt_20 "><?php echo ($page); ?></div>
		<div class="pad_30"></div>
	</div>
	<div class="footer mt_30 min_w">
		<div class="f_14 clr_9 al_ct padt_10 lh_28">

版权所有：青岛三匹马实业有限公司&nbsp;&nbsp;&nbsp;&nbsp;地址：青岛市黄岛区江山南路450号（富安国际大厦）&nbsp;&nbsp;&nbsp;&nbsp;电话：17866631857<br/>



	  Copyright &copy; 2021 鲁ICP备2021017032号-1



 </div>
 
  
<div class="side">
	<ul>
		
		<li class="sideewm"><i class="bgs3"></i>官方微信
			<div class="ewBox son"></div>
		</li>
		<li class="sideetel"><i class="bgs4"></i>联系电话
			<div class="telBox son">
			
				<dd class="bgs2"><span>手机</span>19853227218</dd>
			</div>
		</li>
	
		<li class="sidetop" onClick="goTop()"><i class="bgs6"></i>返回顶部</li>
	</ul>
</div>

<script src="/Public/js/jquery-1.11.0.min.js" type="text/javascript" charset="utf-8"></script>
<script>
	function goTop() {
		$('html,body').animate({
			scrollTop: 0
		}, 500)
	}
</script>

 <style>
 .side{position:fixed;width:60px;right:0;top:60%;margin-top:-200px;z-index:100;border:1px solid #e0e0e0;background:#fff;border-bottom:0}
.side ul li{width:60px;height:70px;float:left;position:relative;border-bottom:1px solid #e0e0e0;color:#333;font-size:14px;line-height:38px;text-align:center;transition:all .3s;cursor:pointer}
.side ul li:hover{background:#f67524;color:#fff}
.side ul li:hover a{color:#fff}
.side ul li i{height:25px;margin-bottom:1px;display:block;overflow:hidden;background-repeat:no-repeat;background-position:center center;background-size:auto 25px;margin-top:14px;transition:all .3s}
.side ul li i.bgs1{background-image:url(/Public/images/right_pic5.png)}
.side ul li i.bgs2{background-image:url(/Public/images/right_pic7.png)}
.side ul li i.bgs3{background-image:url(/Public/images/right_pic2.png)}
.side ul li i.bgs4{background-image:url(/Public/images/right_pic1.png)}
.side ul li i.bgs5{background-image:url(/Public/images/right_pic3.png)}
.side ul li i.bgs6{background-image:url(/Public/images/right_pic6_on.png)}
.side ul li:hover i.bgs1{background-image:url(/Public/images/right_pic5_on.png)}
.side ul li:hover i.bgs2{background-image:url(/Public/images/right_pic7_on.png)}
.side ul li:hover i.bgs3{background-image:url(/Public/images/right_pic2_on.png)}
.side ul li:hover i.bgs4{background-image:url(/Public/images/right_pic1_on.png)}
.side ul li:hover i.bgs5{background-image:url(/Public/images/right_pic3_on.png)}
.side ul li .sidebox{position:absolute;width:78px;height:78px;top:0;right:0;transition:all .3s;overflow:hidden}
.side ul li.sidetop{background:#f67524;color:#fff}
.side ul li.sidetop:hover{opacity:.8;filter:Alpha(opacity=80)}
.side ul li.sideewm .ewBox.son{width:238px;display:none;color:#363636;text-align:center;padding-top:212px;position:absolute;left:-240px;top:0;background-image:url(/Public/images/leftewm.png);background-repeat:no-repeat;background-position:center center;border:1px solid #e0e0e0}
.side ul li.sideetel .telBox.son{width:240px;height:214px;display:none;color:#fff;text-align:left;position:absolute;left:-240px;top:-72px;background:#f67524}
.side ul li.sideetel .telBox dd{display:block;height:118.5px;overflow:hidden;padding-left:82px;line-height:24px;font-size:18px}
.side ul li.sideetel .telBox dd span{display:block;line-height:28px;height:28px;overflow:hidden;margin-top:32px;font-size:18px}
.side ul li.sideetel .telBox dd.bgs1{background:url(/Public/images/right_pic8.png) 28px center no-repeat;background-color:#e96410}
.side ul li.sideetel .telBox dd.bgs2{background:url(/Public/images/right_pic9.png) 28px center no-repeat}
.side ul li:hover .son{display:block!important;animation:fadein 1s}
@keyframes fadein{from{opacity:0}
to{opacity:1}
}
</style>
	</div>
</body>
</html>