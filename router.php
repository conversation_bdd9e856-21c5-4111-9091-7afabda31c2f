<?php
// router.php - 修复特殊字符问题
$documentRoot = __DIR__;
$requestUri = $_SERVER['REQUEST_URI']; // 保留原始编码

// 1. 提取路径和查询字符串（不自动解码）
$parts = explode('?', $requestUri, 2);
$path = $parts[0];
$query = $parts[1] ?? '';

// 2. 安全过滤路径遍历攻击
if (strpos($path, '..') !== false || strpos($path, "\0") !== false) {
    http_response_code(403);
    exit('Forbidden');
}

// 3. 检查静态文件是否存在（使用原始编码路径）
$filePath = $documentRoot . $path;
if (file_exists($filePath) && is_file($filePath)) {
    return false; // 让服务器处理静态文件
}

// 4. 修复核心问题：保留原始编码的路径
// 重要！不使用 parse_url 解码，直接传递原始路径
$_GET['s'] = ltrim($path, '/');

// 5. 修复查询字符串解析（避免 | 被破坏）
parse_str(urldecode($query), $queryParams); // 先解码再解析
$_GET = array_merge($_GET, $queryParams);

// 6. 修正服务器变量（框架兼容性）
$_SERVER['SCRIPT_NAME'] = '/index.php';
$_SERVER['SCRIPT_FILENAME'] = $documentRoot . '/index.php';
$_SERVER['REQUEST_URI'] = $path . ($query ? '?' . $query : '');

// 7. 包含入口文件
require $documentRoot . '/index.php';
exit;
