/*
 Navicat Premium Data Transfer

 Source Server         : 56
 Source Server Type    : MySQL
 Source Server Version : 50651 (5.6.51)
 Source Host           : localhost:3306
 Source Schema         : sql_mianzhida

 Target Server Type    : MySQL
 Target Server Version : 50651 (5.6.51)
 File Encoding         : 65001

 Date: 05/08/2025 14:55:37
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sd_premium_discount_condition_field
-- ----------------------------
DROP TABLE IF EXISTS `sd_premium_discount_condition_field`;
CREATE TABLE `sd_premium_discount_condition_field` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `field_code` varchar(50) NOT NULL COMMENT '字段代码(如weight, region)',
  `field_name` varchar(100) NOT NULL COMMENT '字段名称(如重量, 地区)',
  `field_type` enum('number','string','select') NOT NULL DEFAULT 'string' COMMENT '字段类型(number=数值型, string=字符串型, select=选择型)',
  `field_unit` varchar(20) DEFAULT NULL COMMENT '字段单位(如kg, %, 等)',
  `select_options` text COMMENT '选择项配置(JSON格式，当field_type=select时使用)',
  `is_range` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否支持范围(1=支持范围, 0=单值)',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用(1=启用, 0=禁用)',
  `sort_order` int(11) NOT NULL DEFAULT '100' COMMENT '排序权重',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_field_code` (`field_code`) COMMENT '字段代码唯一索引',
  KEY `idx_enabled_sort` (`is_enabled`,`sort_order`) COMMENT '启用状态和排序索引'
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8 COMMENT='升贴水条件字段管理表';

-- ----------------------------
-- Records of sd_premium_discount_condition_field
-- ----------------------------
BEGIN;
INSERT INTO `sd_premium_discount_condition_field` (`id`, `field_code`, `field_name`, `field_type`, `field_unit`, `select_options`, `is_range`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES (5, 'cangchumingcheng', '交割仓库', 'select', '', '[\"国家粮食与物资储备局河南局四三二处\",\"河南豫棉物流有限公司\",\"中储棉漯河有限公司\",\"国家粮食和物资储备局湖北局三三八处\",\"衡水市棉麻总公司储备库\",\"菏泽市棉麻经贸开发总公司,菏泽市棉麻公司巨野棉麻站\",\"芜湖市棉麻有限责任公司\",\"滨州中纺银泰实业有限公司\",\"中棉集团山东物流园有限公司\",\"中国供销集团南通供销产业发展有限公司\",\"江阴市协丰棉麻有限公司\",\"江苏银隆仓储物流有限公司\",\"张家港保税区外商投资服务有限公司\",\"库尔勒银星物流有限责任公司\",\"新疆农资（集团）有限责任公司\",\"新疆银棉储运有限公司\",\"中储棉库尔勒有限责任公司\",\"新疆汇锦物流有限公司\",\"新疆伊犁州陆德棉麻有限责任公司\",\"新疆中锦胡杨河仓储物流有限公司\",\"新疆兵棉宏泰物流有限公司\",\"阿拉尔市鹏宇棉花仓储物流有限责任公司\",\"阿克苏益康仓储物流有限公司\",\"阿克苏银星物流有限公司\",\"新疆中新建现代物流股份有限公司\"]', 0, 1, 100, '2025-08-04 05:43:10', '2025-08-05 10:24:36');
INSERT INTO `sd_premium_discount_condition_field` (`id`, `field_code`, `field_name`, `field_type`, `field_unit`, `select_options`, `is_range`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES (6, 'yanseji_pinji', '颜色级', 'select', '', '[\"白棉1级\",\"白棉2级\",\"白棉3级\",\"白棉4级\",\"白棉5级\",\"淡点污棉1级\",\"淡点污棉2级\",\"淡点污棉3级\",\"淡黄染棉1级\",\"淡黄染棉2级\",\"淡黄染棉3级\"]', 0, 1, 20, '2025-08-04 05:43:10', '2025-08-05 09:33:58');
INSERT INTO `sd_premium_discount_condition_field` (`id`, `field_code`, `field_name`, `field_type`, `field_unit`, `select_options`, `is_range`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES (7, 'changdu', '长度', 'number', 'mm', NULL, 1, 1, 30, '2025-08-04 05:43:10', '2025-08-05 09:34:19');
INSERT INTO `sd_premium_discount_condition_field` (`id`, `field_code`, `field_name`, `field_type`, `field_unit`, `select_options`, `is_range`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES (8, 'qiangli', '强度', 'number', 'cN/tex', NULL, 1, 1, 50, '2025-08-04 05:43:10', '2025-08-05 09:37:20');
INSERT INTO `sd_premium_discount_condition_field` (`id`, `field_code`, `field_name`, `field_type`, `field_unit`, `select_options`, `is_range`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES (9, 'subject_level', '主体级', 'number', '', NULL, 0, 1, 10, '2025-08-05 09:33:44', '2025-08-05 09:33:44');
INSERT INTO `sd_premium_discount_condition_field` (`id`, `field_code`, `field_name`, `field_type`, `field_unit`, `select_options`, `is_range`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES (10, 'mazhi', '码值', 'number', '', NULL, 1, 1, 40, '2025-08-05 09:35:18', '2025-08-05 09:35:26');
INSERT INTO `sd_premium_discount_condition_field` (`id`, `field_code`, `field_name`, `field_type`, `field_unit`, `select_options`, `is_range`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES (11, 'zhengqidu', '长整', 'number', '', NULL, 1, 1, 60, '2025-08-05 09:38:14', '2025-08-05 09:38:14');
INSERT INTO `sd_premium_discount_condition_field` (`id`, `field_code`, `field_name`, `field_type`, `field_unit`, `select_options`, `is_range`, `is_enabled`, `sort_order`, `created_at`, `updated_at`) VALUES (12, 'ygzl', '轧工质量', 'select', '', '[\"P1\",\"P2\",\"P3\"]', 0, 1, 70, '2025-08-05 09:39:25', '2025-08-05 09:56:22');
COMMIT;

-- ----------------------------
-- Table structure for sd_premium_discount_rule
-- ----------------------------
DROP TABLE IF EXISTS `sd_premium_discount_rule`;
CREATE TABLE `sd_premium_discount_rule` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_name` varchar(255) NOT NULL COMMENT '规则名称',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用(1=启用, 0=禁用)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_rule_name` (`rule_name`) COMMENT '规则名称唯一索引'
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COMMENT='升贴水计算规则表';

-- ----------------------------
-- Records of sd_premium_discount_rule
-- ----------------------------
BEGIN;
INSERT INTO `sd_premium_discount_rule` (`id`, `rule_name`, `is_enabled`, `created_at`, `updated_at`) VALUES (1, '测试', 1, '2025-08-04 13:52:46', '2025-08-04 14:11:39');
INSERT INTO `sd_premium_discount_rule` (`id`, `rule_name`, `is_enabled`, `created_at`, `updated_at`) VALUES (2, '交割仓库计算规则', 1, '2025-08-05 10:15:34', '2025-08-05 10:15:34');
COMMIT;

-- ----------------------------
-- Table structure for sd_premium_discount_rule_detail
-- ----------------------------
DROP TABLE IF EXISTS `sd_premium_discount_rule_detail`;
CREATE TABLE `sd_premium_discount_rule_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_id` int(11) NOT NULL COMMENT '关联规则ID',
  `field_id` int(11) NOT NULL COMMENT '关联条件字段ID',
  `condition_type` enum('single','range') NOT NULL DEFAULT 'single' COMMENT '条件类型(single=单值, range=范围)',
  `operator` enum('>','<','=','>=','<=','!=','between','in') NOT NULL COMMENT '条件操作符',
  `condition_value` varchar(255) DEFAULT NULL COMMENT '条件值(单值时使用)',
  `min_value` varchar(100) DEFAULT NULL COMMENT '最小值(范围时使用)',
  `max_value` varchar(100) DEFAULT NULL COMMENT '最大值(范围时使用)',
  `discount_value` decimal(10,2) NOT NULL COMMENT '升贴水值',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_rule_id` (`rule_id`) COMMENT '规则ID索引',
  KEY `idx_field_id` (`field_id`) COMMENT '字段ID索引',
  CONSTRAINT `sd_premium_discount_rule_detail_ibfk_1` FOREIGN KEY (`rule_id`) REFERENCES `sd_premium_discount_rule` (`id`) ON DELETE CASCADE,
  CONSTRAINT `sd_premium_discount_rule_detail_ibfk_2` FOREIGN KEY (`field_id`) REFERENCES `sd_premium_discount_condition_field` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8 COMMENT='升贴水规则详情表';

-- ----------------------------
-- Records of sd_premium_discount_rule_detail
-- ----------------------------
BEGIN;
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (1, 1, 6, 'single', '=', '白棉1级', NULL, NULL, 300.00, '2025-08-04 15:13:03', '2025-08-05 09:42:00');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (2, 1, 6, 'single', '=', '白棉2级', NULL, NULL, 15.00, '2025-08-04 15:13:18', '2025-08-05 09:42:50');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (3, 1, 6, 'single', '=', '白棉3级', NULL, NULL, 0.00, '2025-08-04 15:13:36', '2025-08-05 09:42:58');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (5, 1, 6, 'single', '=', '淡点污棉1级', NULL, NULL, -150.00, '2025-08-04 15:16:10', '2025-08-04 15:16:10');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (6, 1, 6, 'single', '=', '淡点污棉2级', NULL, NULL, -400.00, '2025-08-04 15:16:25', '2025-08-04 15:16:25');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (7, 1, 6, 'single', '=', '淡点污棉3级', NULL, NULL, -800.00, '2025-08-04 15:16:43', '2025-08-04 15:16:43');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (8, 1, 6, 'single', '=', '淡黄染棉1级', NULL, NULL, -700.00, '2025-08-04 15:17:00', '2025-08-04 15:17:00');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (9, 1, 6, 'single', '=', '淡黄染棉2级', NULL, NULL, -1100.00, '2025-08-04 15:17:37', '2025-08-04 15:17:37');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (10, 1, 6, 'single', '=', '淡黄染棉3级', NULL, NULL, -1600.00, '2025-08-04 15:17:52', '2025-08-04 15:17:52');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (12, 1, 7, 'range', 'between', NULL, '26', '26.9', -850.00, '2025-08-04 15:48:53', '2025-08-04 15:48:53');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (13, 1, 7, 'range', 'between', NULL, '27', '27.9', -250.00, '2025-08-04 15:49:24', '2025-08-04 15:49:24');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (14, 1, 7, 'range', 'between', NULL, '29', '29.9', 50.00, '2025-08-04 15:49:52', '2025-08-04 15:49:52');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (15, 1, 7, 'range', 'between', NULL, '30', '30.9', 100.00, '2025-08-04 15:50:14', '2025-08-04 15:50:14');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (16, 1, 7, 'range', 'between', NULL, '31', '31.9', 150.00, '2025-08-04 15:50:57', '2025-08-04 15:50:57');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (19, 1, 7, 'single', '>=', '32', NULL, NULL, 200.00, '2025-08-04 16:01:24', '2025-08-04 16:01:24');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (20, 1, 9, 'single', '>', '80', NULL, NULL, 100.00, '2025-08-05 09:41:24', '2025-08-05 09:41:24');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (21, 1, 6, 'single', '=', '白棉4级', NULL, NULL, -344.40, '2025-08-05 09:43:21', '2025-08-05 09:43:21');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (22, 1, 10, 'range', 'between', NULL, '3.7', '4.2', 100.00, '2025-08-05 09:46:54', '2025-08-05 09:46:54');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (23, 1, 10, 'single', '>=', '5', NULL, NULL, -100.00, '2025-08-05 09:50:59', '2025-08-05 09:50:59');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (24, 1, 8, 'single', '>=', '31', NULL, NULL, 300.00, '2025-08-05 09:51:32', '2025-08-05 09:51:32');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (25, 1, 8, 'range', 'between', NULL, '29', '30.9', 10.00, '2025-08-05 09:52:45', '2025-08-05 09:52:45');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (26, 1, 8, 'range', 'between', NULL, '24', '25.9', -250.00, '2025-08-05 09:53:26', '2025-08-05 09:53:26');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (27, 1, 11, 'single', '>=', '86', NULL, NULL, 300.00, '2025-08-05 09:53:57', '2025-08-05 09:53:57');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (28, 1, 11, 'range', 'between', NULL, '83', '85.9', 200.00, '2025-08-05 09:54:23', '2025-08-05 09:54:23');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (29, 1, 11, 'range', 'between', NULL, '77', '79.9', -200.00, '2025-08-05 09:54:55', '2025-08-05 09:54:55');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (30, 1, 12, 'single', '=', 'P1', NULL, NULL, 100.00, '2025-08-05 09:56:49', '2025-08-05 09:56:49');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (31, 1, 12, 'single', '=', 'P3', NULL, NULL, -300.00, '2025-08-05 09:57:06', '2025-08-05 09:57:06');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (32, 2, 5, 'single', '=', '国家粮食与物资储备局河南局四三二处', NULL, NULL, 700.00, '2025-08-05 10:16:39', '2025-08-05 10:16:39');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (33, 2, 5, 'single', '=', '河南豫棉物流有限公司', NULL, NULL, 700.00, '2025-08-05 10:16:54', '2025-08-05 10:16:54');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (34, 2, 5, 'single', '=', '中储棉漯河有限公司', NULL, NULL, 700.00, '2025-08-05 10:17:10', '2025-08-05 10:17:10');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (35, 2, 5, 'single', '=', '国家粮食和物资储备局湖北局三三八处', NULL, NULL, 750.00, '2025-08-05 10:17:28', '2025-08-05 10:17:28');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (36, 2, 5, 'single', '=', '衡水市棉麻总公司储备库', NULL, NULL, 750.00, '2025-08-05 10:17:52', '2025-08-05 10:17:52');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (37, 2, 5, 'single', 'in', '菏泽市棉麻经贸开发总公司,菏泽市棉麻公司巨野棉麻站', NULL, NULL, 750.00, '2025-08-05 10:18:13', '2025-08-05 10:24:48');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (38, 2, 5, 'single', '=', '芜湖市棉麻有限责任公司', NULL, NULL, 750.00, '2025-08-05 10:18:33', '2025-08-05 10:18:33');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (39, 2, 5, 'single', '=', '滨州中纺银泰实业有限公司', NULL, NULL, 750.00, '2025-08-05 10:19:01', '2025-08-05 10:19:01');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (40, 2, 5, 'single', '=', '中棉集团山东物流园有限公司', NULL, NULL, 750.00, '2025-08-05 10:19:21', '2025-08-05 10:19:21');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (41, 2, 5, 'single', '=', '中国供销集团南通供销产业发展有限公司', NULL, NULL, 800.00, '2025-08-05 10:19:42', '2025-08-05 10:19:42');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (42, 2, 5, 'single', '=', '江阴市协丰棉麻有限公司', NULL, NULL, 800.00, '2025-08-05 10:20:05', '2025-08-05 10:20:05');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (43, 2, 5, 'single', '=', '江苏银隆仓储物流有限公司', NULL, NULL, 800.00, '2025-08-05 10:20:31', '2025-08-05 10:20:31');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (44, 2, 5, 'single', '=', '张家港保税区外商投资服务有限公司', NULL, NULL, 800.00, '2025-08-05 10:20:45', '2025-08-05 10:20:45');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (45, 2, 5, 'single', '=', '阿拉尔市鹏宇棉花仓储物流有限责任公司', NULL, NULL, -50.00, '2025-08-05 10:21:08', '2025-08-05 10:21:08');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (46, 2, 5, 'single', '=', '阿克苏益康仓储物流有限公司', NULL, NULL, -50.00, '2025-08-05 10:21:24', '2025-08-05 10:21:24');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (47, 2, 5, 'single', '=', '阿克苏银星物流有限公司', NULL, NULL, -50.00, '2025-08-05 10:21:40', '2025-08-05 10:21:40');
INSERT INTO `sd_premium_discount_rule_detail` (`id`, `rule_id`, `field_id`, `condition_type`, `operator`, `condition_value`, `min_value`, `max_value`, `discount_value`, `created_at`, `updated_at`) VALUES (48, 2, 5, 'single', '=', '新疆中新建现代物流股份有限公司', NULL, NULL, -100.00, '2025-08-05 10:21:54', '2025-08-05 10:21:54');
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
